import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_controls_library/widgets/text_widget.dart';

void main() {
  group('TextWidget Border Tests', () {
    testWidgets('should have correct default border colors and widths', (WidgetTester tester) async {
      const textWidget = TextWidget(
        key: Key('test-text-widget'),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: textWidget,
          ),
        ),
      );

      // Verify the widget is created with correct defaults
      expect(find.byKey(const Key('test-text-widget')), findsOneWidget);
      
      // Check default values
      expect(textWidget.borderColor, const Color(0xFFCCCCCC));
      expect(textWidget.focusedBorderColor, const Color(0xFF0058FF));
      expect(textWidget.borderWidth, 1.0);
      expect(textWidget.focusedBorderWidth, 1.0);
    });

    testWidgets('should use 1px border width for focus state', (WidgetTester tester) async {
      const textWidget = TextWidget(
        key: Key('test-text-widget'),
        autofocus: true,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: textWidget,
          ),
        ),
      );

      await tester.pump();

      // Find the TextField widget
      final textField = find.byType(TextField);
      expect(textField, findsOneWidget);

      // Verify the widget exists and has focus
      expect(find.byKey(const Key('test-text-widget')), findsOneWidget);
    });

    testWidgets('should use correct border color for hover and focus', (WidgetTester tester) async {
      const textWidget = TextWidget(
        key: Key('test-text-widget'),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: textWidget,
          ),
        ),
      );

      // Verify the widget has the correct focus border color
      expect(textWidget.focusedBorderColor, const Color(0xFF0058FF));
    });

    testWidgets('should create widget from JSON with correct border settings', (WidgetTester tester) async {
      final jsonData = {
        'key': 'test-json-widget',
        'focusedBorderColor': '0xFF0058FF',
        'focusedBorderWidth': '1.0',
        'borderWidth': '1.0',
      };

      final textWidget = TextWidget.fromJson(jsonData);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: textWidget,
          ),
        ),
      );

      // Verify the widget is created with correct JSON values
      expect(find.byKey(const Key('test-json-widget')), findsOneWidget);
      expect(textWidget.focusedBorderColor, const Color(0xFF0058FF));
      expect(textWidget.focusedBorderWidth, 1.0);
      expect(textWidget.borderWidth, 1.0);
    });
  });
}
